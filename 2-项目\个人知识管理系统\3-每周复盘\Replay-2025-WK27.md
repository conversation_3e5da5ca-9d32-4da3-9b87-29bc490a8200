---
homePageLink: "[[PKMS-首页]]"
本周计划: "[[Plan-2025-WK26]]"
---
# 1.  [[字段提示信息表#成果验收 59b696|验收成果]]
> [!dashboard]
> 
> > [!todo] 目标
> > ```dataviewjs
> >// 配置：设置主要关注的标签类型
> >const primaryTag = "#目标"; // 当日程条目都已标记时，筛选此标签
> >// 获取当前笔记文件名并解析周数信息
> >const fileName = dv.current().file.name;
> >const weekMatch = fileName.match(/Replay-(\d{4})-WK(\d+)/);
> >const planFileName = `${fileName.replace("Replay","Plan")}.md`;
> >if (!weekMatch) {
> >    dv.paragraph("**错误**：文件名格式不符合要求：Replay-YYYY-WKWW");
> >} else {
> >    const year = parseInt(weekMatch[1]);
> >    const currentWeek = parseInt(weekMatch[2]);
> >    const projectName = dv.current().file.path.split("/")[1];
> >    const path = `2-项目/${projectName}/2-每日执行`;
> >    // 计算ISO周数的函数
> >    function getISOWeek(date) {
> >        const d = new Date(date);
> >        d.setHours(0,0,0,0);
> >        d.setDate(d.getDate() + 4 - (d.getDay() || 7));
> >        const yearStart = new Date(d.getFullYear(),0,1);
> >        const weekNo = Math.ceil(((d - yearStart) / 86400000 + 1)/7);
> >        return weekNo;
> >    }
> >    // 获取该路径下的所有文件
> >    const allFiles = dv.pages(`"${path}"`).file;
> >    // 存储结果
> >    const untaggedFiles = new Map(); // 未打标签的文件
> >    const primaryTagItems = [];      // 主要标签的条目原始内容
> >    let hasUntaggedEntries = false;  // 是否有未标记条目
> >    let totalEntries = 0;            // 总条目数
> >    // 筛选属于指定年份和周数的文件
> >    for (let file of allFiles) {
> >        const dateMatch = file.name.match(/Do-(\d{4})-(\d{2})-(\d{2})/);
> >        if (!dateMatch) continue;
> >        try {
> >            const dateStr = `${dateMatch[1]}-${dateMatch[2]}-${dateMatch[3]}`;
> >            const fileDate = new Date(dateStr);
> >            if (isNaN(fileDate.getTime())) continue;
> >            // 获取文件的年份和周数
> >            const fileYear = fileDate.getFullYear();
> >            const fileWeek = getISOWeek(fileDate);
> >            // 检查是否匹配当前年份和周数
> >            if (fileYear === year && fileWeek === currentWeek) {
> >                let untaggedCount = 0;
> >                // 检查文件中的列表项
> >                if (file.lists) {
> >                    for (let list of file.lists) {
> >                        if (list.header?.subpath?.includes("输出") && 
> >                            list.task === false &&
> >                            list.text) {
> >                            totalEntries++;
> >                            // 检查条目标签状态
> >                            const hasTag = list.text.includes("#阻碍") || list.text.includes("#技术债") || 
> >                                         list.text.includes("#目标");
> >                            if (!hasTag) {
> >                                untaggedCount++;
> >                                hasUntaggedEntries = true;
> >                            } else if (list.text.includes(primaryTag)) {
> >                                const cleanText = list.text.replace(primaryTag, "").trim();
> >                                primaryTagItems.push(cleanText);
> >                            }
> >                        }
> >                    }
> >                }
> >                // 记录未打标签的文件
> >                if (untaggedCount > 0) {
> >                    untaggedFiles.set(file.path, {
> >                        fileName: file.name,
> >                        count: untaggedCount
> >                    });
> >                }
> >            }
> >        } catch (e) {
> >            console.error("Error processing file:", file.name, e);
> >        }
> >    }
> >    // 智能输出结果
> >    if (untaggedFiles.size > 0) {
> >        // 输出未打标签的文件信息
> >        const fileLinks = [];
> >        for (let [filePath, fileData] of untaggedFiles.entries()) {
> >            const displayName = fileData.fileName.replace(".md", "").replace("Do-", "");
> >            fileLinks.push(`[[${filePath}|${displayName}]] (${fileData.count} 条)`);
> >        }
> >        fileLinks.sort();
> >        dv.paragraph(`以下 ${untaggedFiles.size} 个文件包含未标记条目：`);
> >        dv.paragraph(fileLinks);
> >    } else if (primaryTagItems.length > 0) {
> >        
> >        // 输出主要标签的条目原始信息
> >        dv.el("p",`${primaryTag.replace("#","")}成果：已完成 ${primaryTagItems.length} 个；`);
> >        dv.paragraph(primaryTagItems);
> >       
> >    } else {
> >        if (totalEntries > 0) {
> >            dv.paragraph(`本周无**${primaryTag.replace("#","")}型**成果输出`);
> >        } else {
> >            dv.paragraph("本周没有找到任何条目");
> >        }
> >    }
> >    // 未完成任务统计
> >	const planPath = `2-项目/${projectName}/1-每周规划`;
> >	const planFile = dv.page(`${planPath}/${planFileName}`);
> >	const taskSection = planFile.file.tasks
> >	?.filter(t => t.header?.subpath?.toLowerCase().includes("任务"))
> >	.filter(t =>t.text?.includes(`⟦${primaryTag.replace("#","")}⟧`));
> >	const targetTask = taskSection.filter(t => !t.checked);
> >	// 输出任务
> >	dv.el("p",`${primaryTag.replace("#","")}任务：本周新增任务 ${taskSection.length} 条，` 
> >	+ `其中 ${targetTask.length} 条未完成；`);
> >	dv.taskList(targetTask,false);
> >}
> >```
> 
> > [!tip] 阻碍
> > ```dataviewjs
> >// 配置：设置主要关注的标签类型
> >const primaryTag = "#阻碍"; // 当日程条目都已标记时，筛选此标签
> >// 获取当前笔记文件名并解析周数信息
> >const fileName = dv.current().file.name;
> >const weekMatch = fileName.match(/Replay-(\d{4})-WK(\d+)/);
> >const planFileName = `${fileName.replace("Replay","Plan")}.md`;
> >if (!weekMatch) {
> >    dv.paragraph("**错误**：文件名格式不符合要求：Replay-YYYY-WKWW");
> >} else {
> >    const year = parseInt(weekMatch[1]);
> >    const currentWeek = parseInt(weekMatch[2]);
> >    const projectName = dv.current().file.path.split("/")[1];
> >    const path = `2-项目/${projectName}/2-每日执行`;
> >    // 计算ISO周数的函数
> >    function getISOWeek(date) {
> >        const d = new Date(date);
> >        d.setHours(0,0,0,0);
> >        d.setDate(d.getDate() + 4 - (d.getDay() || 7));
> >        const yearStart = new Date(d.getFullYear(),0,1);
> >        const weekNo = Math.ceil(((d - yearStart) / 86400000 + 1)/7);
> >        return weekNo;
> >    }
> >    // 获取该路径下的所有文件
> >    const allFiles = dv.pages(`"${path}"`).file;
> >    // 存储结果
> >    const untaggedFiles = new Map(); // 未打标签的文件
> >    const primaryTagItems = [];      // 主要标签的条目原始内容
> >    let hasUntaggedEntries = false;  // 是否有未标记条目
> >    let totalEntries = 0;            // 总条目数
> >    // 筛选属于指定年份和周数的文件
> >    for (let file of allFiles) {
> >        const dateMatch = file.name.match(/Do-(\d{4})-(\d{2})-(\d{2})/);
> >        if (!dateMatch) continue;
> >        try {
> >            const dateStr = `${dateMatch[1]}-${dateMatch[2]}-${dateMatch[3]}`;
> >            const fileDate = new Date(dateStr);
> >            if (isNaN(fileDate.getTime())) continue;
> >            // 获取文件的年份和周数
> >            const fileYear = fileDate.getFullYear();
> >            const fileWeek = getISOWeek(fileDate);
> >            // 检查是否匹配当前年份和周数
> >            if (fileYear === year && fileWeek === currentWeek) {
> >                let untaggedCount = 0;
> >                // 检查文件中的列表项
> >                if (file.lists) {
> >                    for (let list of file.lists) {
> >                        if (list.header?.subpath?.includes("输出") && 
> >                            list.task === false &&
> >                            list.text) {
> >                            totalEntries++;
> >                            // 检查条目标签状态
> >                            const hasTag = list.text.includes("#阻碍") || list.text.includes("#技术债") || 
> >                                         list.text.includes("#目标");
> >                            if (!hasTag) {
> >                                untaggedCount++;
> >                                hasUntaggedEntries = true;
> >                            } else if (list.text.includes(primaryTag)) {
> >                                const cleanText = list.text.replace(primaryTag, "").trim();
> >                                primaryTagItems.push(cleanText);
> >                            }
> >                        }
> >                    }
> >                }
> >                // 记录未打标签的文件
> >                if (untaggedCount > 0) {
> >                    untaggedFiles.set(file.path, {
> >                        fileName: file.name,
> >                        count: untaggedCount
> >                    });
> >                }
> >            }
> >        } catch (e) {
> >            console.error("Error processing file:", file.name, e);
> >        }
> >    }
> >    // 智能输出结果
> >    if (untaggedFiles.size > 0) {
> >        // 输出未打标签的文件信息
> >        const fileLinks = [];
> >        for (let [filePath, fileData] of untaggedFiles.entries()) {
> >            const displayName = fileData.fileName.replace(".md", "").replace("Do-", "");
> >            fileLinks.push(`[[${filePath}|${displayName}]] (${fileData.count} 条)`);
> >        }
> >        fileLinks.sort();
> >        dv.paragraph(`以下 ${untaggedFiles.size} 个文件包含未标记条目：`);
> >        dv.paragraph(fileLinks);
> >    } else if (primaryTagItems.length > 0) {
> >        
> >        // 输出主要标签的条目原始信息
> >        dv.el("p",`${primaryTag.replace("#","")}成果：已完成 ${primaryTagItems.length} 个；`);
> >        dv.paragraph(primaryTagItems);
> >       
> >    } else {
> >        if (totalEntries > 0) {
> >            dv.paragraph(`本周无**${primaryTag.replace("#","")}型**成果输出`);
> >        } else {
> >            dv.paragraph("本周没有找到任何条目");
> >        }
> >    }
> >    // 未完成任务统计
> >	const planPath = `2-项目/${projectName}/1-每周规划`;
> >	const planFile = dv.page(`${planPath}/${planFileName}`);
> >	const taskSection = planFile.file.tasks
> >	?.filter(t => t.header?.subpath?.toLowerCase().includes("任务"))
> >	.filter(t =>t.text?.includes(`⟦${primaryTag.replace("#","")}⟧`));
> >	const targetTask = taskSection.filter(t => !t.checked);
> >	// 输出任务
> >	dv.el("p",`${primaryTag.replace("#","")}任务：本周新增任务 ${taskSection.length} 条，` 
> >	+ `其中 ${targetTask.length} 条未完成；`);
> >	dv.taskList(targetTask,false);
> >}
> > ```
> 
> > [!warning] 技术债
> > ```dataviewjs
> >// 配置：设置主要关注的标签类型
> >const primaryTag = "#技术债"; // 当日程条目都已标记时，筛选此标签
> >// 获取当前笔记文件名并解析周数信息
> >const fileName = dv.current().file.name;
> >const weekMatch = fileName.match(/Replay-(\d{4})-WK(\d+)/);
> >const planFileName = `${fileName.replace("Replay","Plan")}.md`;
> >if (!weekMatch) {
> >    dv.paragraph("**错误**：文件名格式不符合要求：Replay-YYYY-WKWW");
> >} else {
> >    const year = parseInt(weekMatch[1]);
> >    const currentWeek = parseInt(weekMatch[2]);
> >    const projectName = dv.current().file.path.split("/")[1];
> >    const path = `2-项目/${projectName}/2-每日执行`;
> >    // 计算ISO周数的函数
> >    function getISOWeek(date) {
> >        const d = new Date(date);
> >        d.setHours(0,0,0,0);
> >        d.setDate(d.getDate() + 4 - (d.getDay() || 7));
> >        const yearStart = new Date(d.getFullYear(),0,1);
> >        const weekNo = Math.ceil(((d - yearStart) / 86400000 + 1)/7);
> >        return weekNo;
> >    }
> >    // 获取该路径下的所有文件
> >    const allFiles = dv.pages(`"${path}"`).file;
> >    // 存储结果
> >    const untaggedFiles = new Map(); // 未打标签的文件
> >    const primaryTagItems = [];      // 主要标签的条目原始内容
> >    let hasUntaggedEntries = false;  // 是否有未标记条目
> >    let totalEntries = 0;            // 总条目数
> >    // 筛选属于指定年份和周数的文件
> >    for (let file of allFiles) {
> >        const dateMatch = file.name.match(/Do-(\d{4})-(\d{2})-(\d{2})/);
> >        if (!dateMatch) continue;
> >        try {
> >            const dateStr = `${dateMatch[1]}-${dateMatch[2]}-${dateMatch[3]}`;
> >            const fileDate = new Date(dateStr);
> >            if (isNaN(fileDate.getTime())) continue;
> >            // 获取文件的年份和周数
> >            const fileYear = fileDate.getFullYear();
> >            const fileWeek = getISOWeek(fileDate);
> >            // 检查是否匹配当前年份和周数
> >            if (fileYear === year && fileWeek === currentWeek) {
> >                let untaggedCount = 0;
> >                // 检查文件中的列表项
> >                if (file.lists) {
> >                    for (let list of file.lists) {
> >                        if (list.header?.subpath?.includes("输出") && 
> >                            list.task === false &&
> >                            list.text) {
> >                            totalEntries++;
> >                            // 检查条目标签状态
> >                            const hasTag = list.text.includes("#阻碍") || list.text.includes("#技术债") || 
> >                                         list.text.includes("#目标");
> >                            if (!hasTag) {
> >                                untaggedCount++;
> >                                hasUntaggedEntries = true;
> >                            } else if (list.text.includes(primaryTag)) {
> >                                const cleanText = list.text.replace(primaryTag, "").trim();
> >                                primaryTagItems.push(cleanText);
> >                            }
> >                        }
> >                    }
> >                }
> >                // 记录未打标签的文件
> >                if (untaggedCount > 0) {
> >                    untaggedFiles.set(file.path, {
> >                        fileName: file.name,
> >                        count: untaggedCount
> >                    });
> >                }
> >            }
> >        } catch (e) {
> >            console.error("Error processing file:", file.name, e);
> >        }
> >    }
> >    // 智能输出结果
> >    if (untaggedFiles.size > 0) {
> >        // 输出未打标签的文件信息
> >        const fileLinks = [];
> >        for (let [filePath, fileData] of untaggedFiles.entries()) {
> >            const displayName = fileData.fileName.replace(".md", "").replace("Do-", "");
> >            fileLinks.push(`[[${filePath}|${displayName}]] (${fileData.count} 条)`);
> >        }
> >        fileLinks.sort();
> >        dv.paragraph(`以下 ${untaggedFiles.size} 个文件包含未标记条目：`);
> >        dv.paragraph(fileLinks);
> >    } else if (primaryTagItems.length > 0) {
> >        
> >        // 输出主要标签的条目原始信息
> >        dv.el("p",`${primaryTag.replace("#","")}成果：已完成 ${primaryTagItems.length} 个；`);
> >        dv.paragraph(primaryTagItems);
> >       
> >    } else {
> >        if (totalEntries > 0) {
> >            dv.paragraph(`本周无**${primaryTag.replace("#","")}型**成果输出`);
> >        } else {
> >            dv.paragraph("本周没有找到任何条目");
> >        }
> >    }
> >    // 未完成任务统计
> >	const planPath = `2-项目/${projectName}/1-每周规划`;
> >	const planFile = dv.page(`${planPath}/${planFileName}`);
> >	const taskSection = planFile.file.tasks
> >	?.filter(t => t.header?.subpath?.toLowerCase().includes("任务"))
> >	.filter(t =>t.text?.includes(`⟦${primaryTag.replace("#","")}⟧`));
> >	const targetTask = taskSection.filter(t => !t.checked);
> >	// 输出任务
> >	dv.el("p",`${primaryTag.replace("#","")}任务：本周新增任务 ${taskSection.length} 条，` 
> >	+ `其中 ${targetTask.length} 条未完成；`);
> >	dv.taskList(targetTask,false);
> >}
> > ```
# 2. [[字段提示信息表#KR进度描述 b44aa3|KR进度]] 

| 序号  | 成果（✅）                                                                                                                                                                                                       | 类型  | [[PKMS-首页#1. OKR设定\|关联KR]] | [[字段提示信息表#KR进度描述 b44aa3\|价值描述]]                                                                                                                                                                                                                  | 风险预警                                                                                                                                                                  | 下一步行动                                                              |
| --- | :---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- | --- | -------------------------- | :----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- | --------------------------------------------------------------------------------------------------------------------------------------------------------------------- | ------------------------------------------------------------------ |
| 1   | 1、「每周计划」新增“阻碍回顾”<br>2、「每周计划」新增“技术债”<br>4、「每日执行」删除“技术债”，拆分“闪念”和“输出”<br>5、「每周评审」新增“本周阻碍”统计<br>6、「每周评审」修改“成果验收”内容重构--代码统计；增加未完成“闪念”统计模块；“KR进度”、“阻碍分析”表格新增“类型”字段<br>7、「每周回顾」新增“类型分析”<br>8、「每周回顾」“类型分析”内容重构-代码统计 | 目标  | KR1：探索PKMS系统的基本内容结构（组件）    | 1、计划制定期间通过参考历史阻碍和技术债将风险前置，以提升本周目标达成率，同时减少问题积累<br>2、“闪念”和“输出”拆分可以提升信息捕获效率<br>3、将未完成成果/任务与阻碍关联，可以突出思考路径，并有利于进行根因分析<br>4、通过“任务统计 + 成果统计”可以降低成果梳理的操作难度；增加“技术债”、“阻碍”看板可以与「每周计划」形成闭环，补全成果类型<br>5、阻碍、技术债类型分析是流程改善的有效途径之一<br>6、突然的发现可能是闪念/洞见，也有可能是阻碍或技术债 | 1、尚不清楚最佳阻碍回顾范围<br>2、使用链接方式显示本周新增阻碍，虽然简化了「每周评审」组件内容结构，但弱化了逻辑结构<br>3、目标成果“任务统计”逻辑增加了成果记录难度<br>4、成果清单排序混乱，成果价值映射操作难度增加<br>5、组件看板容易误触，导致源代码显露，影响视线<br>6、看板工具的存在这样加了操作的复杂度 | 1、「每周计划」阻碍回顾范围探索<br>2、看板工具优化（嵌入、链接）<br>3、「每周评审」组件看板代码优化（名称排序、任务统计） |
| 2   | 1、阻碍日志创建脚本<br>2、「技术债工作流」-->验收标准<br>3、技术债任务规划工作流<br>4、技术债创建脚本使用说明<br>5、「技术债」模板<br>6、「每日执行」新增“阻碍”看板<br>7、“过程资产”文件夹结构重构（增加“阻碍”文件夹）                                                                             | 目标  | KR2：探索PKMS系统的核心工作流         | 1、自动化脚本串联了整个工作流，可以更好的摆脱组件的限制<br>2、“独立管理+轻量嵌入”是阻碍、技术债管理的有效途径之一<br>3、执行任务不是产生阻碍的唯一途径，所以阻碍需要脱离「每日执行」独立管理<br>4、验收标准并不一定需要一次性完成，采用“渐进式构建”更符合敏捷原则和实际工作场景                                                                                               | 1、组件看板容易误触，导致源代码显露，影响视线                                                                                                                                               | 1、组件看板优化（代码优化、误触问题）                                                |
| 3   | 1、看板工具（删除“成果统计”、“问题统计”）                                                                                                                                                                                     | 目标  | KR1：探索PKMS系统的基本内容结构（组件）    | 1、较少不必要的内容，可以降低系统负荷                                                                                                                                                                                                                              | 1、成果记录遵循的原则尚不清晰                                                                                                                                                       | 1、「每日执行」成果记录规则探索                                                   |
# 3. 交付异常

| 序号  | 成果/任务（🌗❌） | 类型  | 交付状态 | 关联障碍[[看板工具#^5b7c96\|🚫]] | 根因分析                      | 下一步行动 |
| --- | ---------- | --- | ---- | ------------------------ | ------------------------- | ----- |
| 1   |            |     |      |                          | 哪些关键障碍是导致目标偏移或任务未完成的主要原因？ |       |
# 4. 闪念统计
```dataviewjs
	// 从复盘文件名提取周数 (格式: Replay-YYYY-WKxx)
	const fileName = dv.current().file.name;
	const weekNum = parseInt(fileName.match(/WK(\d+)/)?.[1]);
	if(!weekNum){
		dv.el("p", "❌ 错误：文件名格式不正确，必须是 Replay-YYYY-WKxx 格式");
	}else{
	// 从当前文件路径解析项目名称 (路径格式: 2-项目/项目名/3-每周复盘/...)
	const projectName = dv.current().file.path.split("/")[1]; 
	const path = `2-项目/${projectName}/2-每日执行`;
	const tagOrder = ["风险预警", "流程优化", "创新方案", "新增需求", "问题探索"];
	
	// 获取所有相关文件（周数 <= 当前复盘周）
	const matchingFiles = dv.pages(`"${path}"`).file.filter(file => {
	    // 从每日笔记名提取日期 (格式: Do-YYYY-MM-DD)
	    const dateMatch = file.name.match(/Do-(\d{4}-\d{2}-\d{2})/);
	    if (!dateMatch) return false;
	    
	    // 计算笔记的ISO周数
	    const date = new Date(dateMatch[1]);
	    date.setHours(0, 0, 0, 0);
	    date.setDate(date.getDate() + 4 - (date.getDay() || 7)); // 移动到周四
	    const yearStart = new Date(date.getFullYear(), 0, 1);
	    const fileWeek = Math.ceil(((date - yearStart) / 86400000 + 1) / 7);
	    
	    return fileWeek <= weekNum; // 只保留当前周及之前的笔记
	});
	
	// 获取未处理的闪念任务（逻辑保持不变）
	const allPendingTasks = matchingFiles
	    .flatMap(f => f.tasks || [])
	    .filter(task => 
	        task && 
	        task.text?.trim() && 
	        task.checked &&
	        task.section?.subpath?.toLowerCase().includes("闪念")
	    )
	    .sort((a, b) => {
	        const getTagValue = task => {
	            const text = task.text || "";
	            const tag = tagOrder.find(t => text.includes(`#${t}`));
	            return tag ? tagOrder.indexOf(tag) : tagOrder.length;
	        };
	        return getTagValue(a) - getTagValue(b);
	    });
	
	// 输出结果（保持不变）
	if (allPendingTasks.length > 0) {
	    dv.el("p", `📝 待处理闪念 (${allPendingTasks.length}条)`);
	    dv.taskList(allPendingTasks, false);
	}else{
		dv.el("p","🥳 所有闪念均已处理完")
	}}
```
