# 洞见与可执行任务添加脚本使用说明

## 功能概述

这个脚本可以快速添加洞见或可执行任务到指定文件，具有以下功能：

1. **快速选择类型**：通过弹窗快速选择"洞见"或"可执行任务"
2. **便捷输入内容**：弹出对话框输入任务内容
3. **自动定位**：自动在目标文件中找到对应标题位置
4. **智能添加**：在标题下方添加新任务，保持格式一致

## 文件结构

```
0-辅助/
├── Scripts/
│   ├── addInsightOrTask.js          # 主要脚本逻辑
│   └── 洞见任务添加脚本使用说明.md    # 本说明文件
└── Templater/
    └── Function/
        └── addInsightOrTask.md      # Templater调用接口
```

## 使用方法

### 设置快捷键（一次性设置）

1. **打开Obsidian设置**：点击左下角齿轮图标
2. **进入快捷键设置**：左侧菜单选择"快捷键"
3. **搜索模板**：在搜索框输入"addInsightOrTask"
4. **设置快捷键**：找到"Templater: Open addInsightOrTask.md"，点击右侧添加快捷键
5. **输入组合键**：例如 `Alt+I` 或 `Ctrl+Shift+I`

### 日常使用

1. **按下快捷键**：使用你设置的快捷键（如 `Alt+I`）
2. **选择类型**：在弹出的选择器中选择"洞见"或"可执行任务"
3. **输入内容**：在弹出的对话框中输入任务内容
4. **完成添加**：点击确认后，任务会自动添加到目标文件中

## 目标文件

脚本会将任务添加到以下文件：
```
2-项目/个人知识管理系统/改进待办事项.md
```

## 标题要求

脚本会查找包含"洞见"或"可执行任务"关键词的二级标题（## 开头）。
例如：
- `## 收集的洞见`
- `## 可执行任务列表`

## 故障排除

### 常见问题

1. **"未找到包含'xxx'的标题"**
   - 检查目标文件中是否存在包含关键词的二级标题
   - 确保标题格式为 `## 包含关键词的标题`

2. **"目标文件不存在"**
   - 检查 `2-项目/个人知识管理系统/改进待办事项.md` 是否存在
   - 如需修改目标文件路径，请编辑脚本文件

3. **快捷键不起作用**
   - 确认快捷键设置正确
   - 检查是否有其他插件占用了相同的快捷键

### 调试信息

脚本会在控制台输出详细的错误信息，可通过以下步骤查看：
1. 按 `Ctrl+Shift+I` 打开开发者工具
2. 切换到 Console 标签页
3. 查看红色错误信息

## 自定义修改

如需修改脚本行为，可编辑 `0-辅助/Scripts/addInsightOrTask.js` 文件：

- **修改目标文件路径**：更改 `targetFilePath` 变量
- **修改选项**：更改 `suggester` 函数的参数
- **修改任务格式**：更改 `newTask` 变量的定义