/**
 * 添加洞见或可执行任务到改进待办事项文件
 * 功能：
 * 1. 弹出选择弹窗让用户选择"洞见"或"可执行任务"
 * 2. 弹出输入弹窗让用户输入任务内容
 * 3. 在指定文件的对应标题下添加新任务
 */

module.exports = async (tp) => {
  try {
    // 动态获取目标文件路径
    let targetFilePath;

    // 获取当前活动文件
    const activeFile = app.workspace.getActiveFile();
    if (activeFile) {
      // 从路径中提取项目名称
      const projectName = activeFile.path.split("/")[1];

      targetFilePath = `2-项目/${projectName}/改进待办事项.md`;

      console.log("目标文件路径:", targetFilePath);

      // 步骤1：弹出选择弹窗，让用户选择"洞见"或"可执行任务"
      const taskType = await tp.system.suggester(
        ["【1】洞见/发现", "【2】可执行任务"],
        ["洞见", "可执行任务"]
      );

      // 如果用户取消了选择，则退出脚本
      if (!taskType) {
        new Notice("已取消添加动作");
        return null;
      }

      // 步骤2：弹出输入弹窗，让用户输入任务内容
      const taskContent = await tp.system.prompt(`请输入${taskType}内容:`);

      // 如果用户取消了输入或输入为空，则退出脚本
      if (!taskContent || taskContent.trim() === "") {
        new Notice("任务内容不能为空，已取消添加");
        return null;
      }

      // 获取目标文件
      const targetFile = app.vault.getAbstractFileByPath(targetFilePath);
      if (!targetFile) {
        new Notice(`目标文件不存在: ${targetFilePath}`);
        return null;
      }

      // 读取文件内容
      let fileContent = await app.vault.read(targetFile);

      // 步骤3：查找对应的标题（按关键字精确匹配）
      let headingKeywords;
      if (taskType === "洞见") {
        headingKeywords = ["洞见", "发现", "insight"];
      } else if (taskType === "可执行任务") {
        headingKeywords = ["可执行任务", "任务", "task", "todo"];
      }

      let match = null;
      let matchedKeyword = "";

      // 尝试匹配每个关键字
      for (const keyword of headingKeywords) {
        const headingRegex = new RegExp(
          `## .*${keyword}.*\\n([\\s\\S]*?)(?=\\n##|$)`,
          "i"
        );
        match = fileContent.match(headingRegex);
        if (match) {
          matchedKeyword = keyword;
          break;
        }
      }

      if (!match) {
        new Notice(
          `未找到包含"${taskType}"相关关键字的标题（尝试的关键字：${headingKeywords.join(
            ", "
          )}）`
        );
        return null;
      }

      // 步骤4：在标题下添加新任务
      const headingContent = match[0];
      const headingEndPos =
        fileContent.indexOf(headingContent) + headingContent.length;

      // 创建新任务（确保任务前有空行）
      let newTask = `- [ ] ${taskContent.trim()}`;
      if (!headingContent.endsWith("\n\n")) {
        newTask = "\n" + newTask;
      }

      // 更新文件内容
      const newContent =
        fileContent.substring(0, headingEndPos) +
        newTask +
        "\n" +
        fileContent.substring(headingEndPos);

      // 写入文件
      await app.vault.modify(targetFile, newContent);

      // 显示成功通知
      new Notice(
        `已添加${taskType}: ${taskContent.substring(0, 20)}${
          taskContent.length > 20 ? "..." : ""
        }`
      );

      return true;
    }
  } catch (error) {
    console.error("添加洞见或任务时出错:", error);
    new Notice(`添加失败: ${error.message}`);
    return null;
  }
};
