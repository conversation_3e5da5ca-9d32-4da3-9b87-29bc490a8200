/**
 * 快速记录突发灵感到改进待办事项文件
 * 功能：
 * 1. 弹出选择弹窗让用户选择"洞见"、"可执行任务"或"知识"
 * 2. 弹出输入弹窗让用户输入内容
 * 3. 自动存储在「改进待办事项」"缓存区"
 * 4. 当选择"知识"时，自动在新标签页打开「效率工具箱」
 */

module.exports = async (tp) => {
  try {
    // 动态获取目标文件路径
    let targetFilePath;

    // 获取当前活动文件
    const activeFile = app.workspace.getActiveFile();
    if (activeFile) {
      // 从路径中提取项目名称
      const projectName = activeFile.path.split("/")[1];

      targetFilePath = `2-项目/${projectName}/改进待办事项.md`;

      console.log("目标文件路径:", targetFilePath);

      // 步骤1：弹出选择弹窗，让用户选择类型
      const taskType = await tp.system.suggester(
        ["【1】洞见/发现", "【2】知识"],
        ["洞见", "知识"]
      );

      // 如果用户取消了选择，则退出脚本
      if (!taskType) {
        new Notice("已取消添加动作");
        return null;
      }

      // 步骤2：弹出输入弹窗，让用户输入内容
      const taskContent = await tp.system.prompt(`请输入${taskType}内容:`);

      // 如果用户取消了输入或输入为空，则退出脚本
      if (!taskContent || taskContent.trim() === "") {
        new Notice("内容不能为空，已取消添加");
        return null;
      }

      // 获取目标文件
      const targetFile = app.vault.getAbstractFileByPath(targetFilePath);
      if (!targetFile) {
        new Notice(`目标文件不存在: ${targetFilePath}`);
        return null;
      }

      // 读取文件内容
      let fileContent = await app.vault.read(targetFile);

      // 步骤3：根据类型查找对应标题
      let targetHeading;
      if (taskType === "洞见") {
        targetHeading = "洞见";
      } else if (taskType === "知识") {
        targetHeading = "缓存区";
      }

      const headingRegex = new RegExp(
        `^#+\\s.*${targetHeading}.*\\n([\\s\\S]*?)(?=\\n#+|$)`,
        "im"
      );
      const match = fileContent.match(headingRegex);

      if (!match) {
        new Notice(`未找到"${targetHeading}"标题`);
        return null;
      }

      // 步骤4：在标题下添加新任务
      const headingContent = match[0];
      const headingEndPos =
        fileContent.indexOf(headingContent) + headingContent.length;

      // 创建新条目（根据类型添加标签）
      let newTask = `- [ ] [${taskType}] ${taskContent.trim()}`;
      if (!headingContent.endsWith("\n\n")) {
        newTask = "\n" + newTask;
      }

      // 更新文件内容
      const newContent =
        fileContent.substring(0, headingEndPos) +
        newTask +
        fileContent.substring(headingEndPos);

      // 写入文件
      await app.vault.modify(targetFile, newContent);

      // 如果是知识类型，自动打开「效率工具箱」
      if (taskType === "知识") {
        await openEfficiencyToolbox();
      }

      // 显示成功通知
      new Notice(
        `已添加 ${taskType}: ${taskContent.substring(0, 20)}${
          taskContent.length > 20 ? "..." : ""
        }`
      );

      return true;
    }
  } catch (error) {
    console.error("添加灵感时出错:", error);
    new Notice(`添加失败: ${error.message}`);
    return null;
  }

  // 打开效率工具箱的辅助函数
  async function openEfficiencyToolbox() {
    try {
      const toolboxPath = "1-资源/效率工具箱.md";
      let toolboxFile = app.vault.getAbstractFileByPath(toolboxPath);

      // 如果文件不存在，创建文件
      if (!toolboxFile) {
        await app.vault.create(toolboxPath, "# 效率工具箱\n\n");
        toolboxFile = app.vault.getAbstractFileByPath(toolboxPath);
      } else {
        // 如果文件存在但内容为空白，添加标题
        const content = await app.vault.read(toolboxFile);
        if (!content.trim()) {
          await app.vault.modify(toolboxFile, "# 效率工具箱\n\n");
        }
      }

      // 在新标签页打开文件
      const leaf = app.workspace.getLeaf("tab");
      await leaf.openFile(toolboxFile);
    } catch (error) {
      console.error("打开效率工具箱时出错:", error);
      new Notice(`打开效率工具箱失败: ${error.message}`);
    }
  }
};
