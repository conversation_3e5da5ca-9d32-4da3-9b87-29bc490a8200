---
startDate: 2025-05-26
endDate: 2025-06-30
---

# OKR设定

- O：打造自驱型的仓储知识引擎
- KR1：通过Deepseek提问+启示方式，在obsidian中完成PKMS系统的搭建
- KR2：通过PKMS完成仓储知识的解构
- KR3：实现知识复用

# 项目进度
```dataviewjs
	// 定义 getWeekNumber 函数
	function getWeekNumber(date) {
	    const d = new Date(date);
	    d.setHours(0, 0, 0, 0);
	    d.setDate(d.getDate() + 3 - (d.getDay() + 6) % 7);
	    const week1 = new Date(d.getFullYear(), 0, 4);
	    return 1 + Math.round(((d - week1) / 86400000 - 3 + (week1.getDay() + 6) % 7) / 7);
	}
	
	// 获取当前文件的路径信息，提取项目名称
	const currentFile = dv.current().file;
	const currentPath = currentFile.path;
	const projectName = currentPath.split("/")[1].trim();
	
	// 获取当前日期并计算年份和周数
	const now = new Date();
	const year = now.getFullYear();
	const weekNumber = getWeekNumber(now) -1;
	
	//动态生成文件名
	const dynamicFilename = `Replay-${year}-WK${weekNumber.toString().padStart(2, '0')}.md`;
	const path = `2-项目/${projectName}/3-每周复盘/${dynamicFilename}`;
	const file = app.vault.getAbstractFileByPath(path)
	if(file){
		const targetHeading = "# 2. KR进度";
		//读取并解析文件
		const content = await dv.io.load(path);
		const headingRegex = new RegExp(`(${targetHeading}[^]*?\\n)([^]*?)(?=\\n#|$)`);
		const match = content.match(headingRegex);
		const result = match[2].trim();
		dv.paragraph(result)
	}else{
		dv.el("p","无更新")
	}
```

# 规划导航

| 序号  | 日期         | 每周规划               | 每周复盘                 | 每周回顾                 |
| --- | ---------- | ------------------ | -------------------- | -------------------- |
| 1   | 2025-05-26 | [[Plan-2025-WK22]] | [[Replay-2025-WK22]] | [[Review-2025-WK22]] |
| 2   | 2025-06-02 | [[Plan-2025-WK23]] |                      |                      |
|     |            |                    |                      |                      |
