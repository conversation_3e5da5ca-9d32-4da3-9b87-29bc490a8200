---
homePageLink: "[[PKMS-首页]]"
---
# 1. 成果验收 [[字段提示信息表#^19887e|?]] 
```dataviewjs
	const heading = "验收标准";
	const projectName = dv.current().file.path.split("/")[1].trim();
	const fileName = dv.current().file.name.replace("Replay","Plan").trim();
	const path = `2-项目/${projectName}/1-每周规划/${fileName}.md`;
	const tasks = dv.page(path).file.tasks
		.filter(t => t.text)
		.filter(t => t.section && t.section.subpath.includes(heading));
	dv.taskList(tasks,false)
```
# 2. KR进度 [[字段提示信息表#^b44aa3|?]] 

[KR1：通过Deepseek提问+启示方式，在obsidian中完成PKMS系统的搭建]
- 使项目推进更加自然顺畅、降低了项目推进的难度
	- [项目首页]增加“项目进展”模块
	- [每周规划]增加“本周方向”模块
	- [每日执行]增加“目标异动”、“技术债”、“闪念”模块
	- [每周复盘]增加“目标异动（历史汇总）”、“闪念（历史汇总）”、“技术债（历史汇总）”模块
	- [每周回顾]增加“阻碍（历史汇总）”、“根因分析”模块
- 形成知识解构的基本框架和可能的记录方式
	- [[TP-概念]]
	- [[TP-流程方法]]
	- [[TP-Knowledge--案例记录]]
	- [[TP-条件策略]] 

# 3. 目标异动
```dataviewjs
	// 汇总表格数据到当前页面
	const projectName= dv.current().file.path.split("/")[1].trim();
	const targetFolder = `2-项目/${projectName}/2-每日执行`;
	const tableTitlePattern = "目标异动"; // 标题中包含的关键词
	
	// 1. 从当前文件名确定周数 (格式: Replay-2025-WK22)
	const currentFileName = dv.current().file.name;
	const weekMatch = currentFileName.match(/Replay-(\d{4})-WK(\d{2})/);
	const targetYear = parseInt(weekMatch[1]);
	const targetWeek = parseInt(weekMatch[2]);
	dv.el("p", `截止第 ${targetWeek} 周的数据汇总`);
	
	// 2. 修正周数计算函数 (ISO周标准)
	const getWeekDates = (year, week) => {
	    // 创建1月4日的日期 (ISO周定义: 包含1月4日的那周是第一周)
	    const jan4 = new Date(year, 0, 4);
	    const jan4Day = jan4.getDay() || 7; // 转换周日(0)为7
	    
	    // 计算第一周的周一
	    const firstMonday = new Date(jan4);
	    firstMonday.setDate(jan4.getDate() - jan4Day + 1);
	    
	    // 计算目标周的周一
	    const targetMonday = new Date(firstMonday);
	    targetMonday.setDate(firstMonday.getDate() + (week - 1) * 7);
	    
	    // 计算目标周的周日
	    const targetSunday = new Date(targetMonday);
	    targetSunday.setDate(targetMonday.getDate() + 6);
	    
	    return { start: targetMonday, end: targetSunday };
	};
	
	const weekDates = getWeekDates(targetYear, targetWeek);
	const formatDate = date => {
	    const y = date.getFullYear();
	    const m = String(date.getMonth() + 1).padStart(2, '0');
	    const d = String(date.getDate()).padStart(2, '0');
	    return `${y}-${m}-${d}`;
	};
	
	const startDateStr = formatDate(weekDates.start);
	const endDateStr = formatDate(weekDates.end);
	
	let allTableData = [];
	let filesProcessed = 0;
	let filesInScope = 0;
	let headersFound = 0;
	let tablesFound = 0;
	let validTables = 0;
	
	// 3. 处理小于等于目标周结束日期的所有文件
	for (let file of dv.pages(`"${targetFolder}"`).file) {
	    filesProcessed++;
	    
	    // 检查文件名格式 (Do-YYYY-MM-DD)
	    const dateMatch = file.name.match(/Do-(\d{4})-(\d{2})-(\d{2})/);
	    if (!dateMatch) continue;
	    
	    const fileYear = parseInt(dateMatch[1]);
	    const fileMonth = parseInt(dateMatch[2]) - 1; // 月份从0开始
	    const fileDay = parseInt(dateMatch[3]);
	    const fileDate = new Date(fileYear, fileMonth, fileDay);
	    
	    // 检查文件是否在目标周结束日期之前或当天
	    if (fileDate > weekDates.end) continue;
	    
	    filesInScope++;
	    const content = await dv.io.load(file.path);
	    
	    // 查找包含目标关键词的标题
	    const headingRegex = new RegExp(`\\n#+\\s*([\\d.]*\\s*)?${tableTitlePattern}[^\\n]*\\n([\\s\\S]*?)(?=\\n#|$)`, "i");
	    const match = content.match(headingRegex);
	    
	    if (!match || !match[2]) continue;
	    headersFound++;
	    
	    const sectionContent = match[2].trim();
	    const tableRegex = /^\s*\|(.+\|.+\|)(?:\n\s*\|([ |:-]*\|)+\s*)?((?:\n\s*\|.+\|)+)/gms;
	    let tableMatch;
	    
	    while ((tableMatch = tableRegex.exec(sectionContent)) !== null) {
	        tablesFound++;
	        const tableContent = tableMatch[0].trim();
	        const tableRows = tableContent.split('\n').filter(line => 
	            line.trim().startsWith('|') && !line.includes('---')
	        );
	        
	        if (tableRows.length > 0) {
	            // 获取表头列数作为参考
	            const headerCols = tableRows[0].split('|')
	                .map(c => c.trim())
	                .filter(c => c !== '');
	            const expectedColCount = headerCols.length;
	            
	            const processedRows = [];
	            let hasValidRows = false; // 标记表格中是否有有效数据行
	            
	            for (let i = 0; i < tableRows.length; i++) {
	                let cols = tableRows[i].split('|')
	                    .map(c => c.trim())
	                    .filter(c => c !== '');
	                
	                // 修复列数不匹配问题
	                if (cols.length > expectedColCount) {
	                    cols = cols.slice(0, expectedColCount);
	                } else if (cols.length < expectedColCount) {
	                    // 补充缺失的列
	                    const missingCols = expectedColCount - cols.length;
	                    cols = [...cols, ...Array(missingCols).fill('')];
	                }
	                
	                // 如果是第一行（表头）直接加入
	                if (i === 0) {
	                    processedRows.push(cols);
	                } 
	                // 检查数据行（跳过表头）是否有有效内容
	                else {
	                    // 检查除了第一列（序号列）外是否有数据
	                    let hasData = false;
	                    for (let j = 1; j < cols.length; j++) {
	                        if (cols[j] && cols[j].trim() !== '') {
	                            hasData = true;
	                            break;
	                        }
	                    }
	                    
	                    if (hasData) {
	                        processedRows.push(cols);
	                        hasValidRows = true;
	                    }
	                }
	            }
	            
	            // 只有包含有效数据行的表格才加入汇总
	            if (processedRows.length > 1 && hasValidRows) {
	                allTableData.push({
	                    file: file,
	                    title: match[1] || tableTitlePattern,
	                    rows: processedRows
	                });
	                validTables++;
	            }
	        }
	    }
	}
	
	// 4. 创建Dataview表格对象（修复链接点击问题）
	if (allTableData.length > 0) {
	    // 准备表头
	    const headers = [...allTableData[0].rows[0]];
	    headers[0] = "序号";
	    headers.push("来源");
	    
	    // 准备数据行
	    const data = [];
	    let rowIndex = 1;
	    
	    for (let table of allTableData) {
	        for (let i = 1; i < table.rows.length; i++) {
	            const rowData = [...table.rows[i]];
	            rowData[0] = rowIndex.toString();
	            
	            // 直接使用文件对象创建Dataview链接
	            rowData.push(table.file.link);
	            
	            data.push(rowData);
	            rowIndex++;
	        }
	    }
	    
	    // 渲染Dataview表格
	    dv.table(headers, data);
	} else {
	    dv.el("p", `截止第${targetWeek}周之前的 ${filesInScope} 个文件中，未找到符合要求的表格数据。`);
	}
```

# 4. 闪念
```dataviewjs
	const projectName = dv.current().file.path.split("/")[1].trim();
	const folder = `2-项目/${projectName}/2-每日执行`;
	
	// 1. 从当前文件名提取周数
	const currentFileName = dv.current().file.name;
	const replayMatch = currentFileName.match(/Replay-(\d{4})-WK(\d{2})/);
	
	//数组解构赋值，replayMatch的值为["Replay-2025-WK22", "2025", "22", index: 0, input: "Replay-2025-WK22", groups: undefined]，其中[,]第一参数表示“跳过第一个元素”
	const [, currentYear, currentWeekStr] = replayMatch;
	const currentWeek = parseInt(currentWeekStr);
	
	// 2. 筛选目标文件
	const tasks = dv.pages(`"${folder}"`).file
		.filter(file => {
			const match = file.name.match(/Do-(\d{4})-(\d{2})-(\d{2})/);//示例["Do-2025-06-08", "2025", "06", "08"]
			if (!match) return false;
			
			const date = moment(match.slice(1, 4).join('-'));
			const fileYear = date.isoWeekYear();
			const fileWeek = date.isoWeek();
			
			return fileYear.toString() === currentYear && fileWeek <= currentWeek;
		})
		.flatMap(file => file.tasks || []) // 正确访问每个文件的tasks
		.filter(t => t.text) // 过滤有文本的任务
		.filter(t => t.section?.subpath?.toLowerCase().includes("闪念")); // 过滤"闪念"标题下的任务
	// 3. 显示结果
	if (tasks.length > 0) {
		dv.el("p", `截止第${currentWeek}周的数据汇总`);
		dv.taskList(tasks, false);
	} else {
		dv.el("p", `📭 未找到闪念`);
	}
```
# 5.技术债
```dataviewjs
	const projectName = dv.current().file.path.split("/")[1].trim();
	const folder = `2-项目/${projectName}/2-每日执行`;
	
	// 1. 从当前文件名提取周数
	const currentFileName = dv.current().file.name;
	const replayMatch = currentFileName.match(/Replay-(\d{4})-WK(\d{2})/);
	
	//数组解构赋值，replayMatch的值为["Replay-2025-WK22", "2025", "22", index: 0, input: "Replay-2025-WK22", groups: undefined]，其中[,]第一参数表示“跳过第一个元素”
	const [, currentYear, currentWeekStr] = replayMatch;
	const currentWeek = parseInt(currentWeekStr);
	
	// 2. 筛选目标文件
	const tasks = dv.pages(`"${folder}"`).file
		.filter(file => {
			const match = file.name.match(/Do-(\d{4})-(\d{2})-(\d{2})/);//示例["Do-2025-06-08", "2025", "06", "08"]
			if (!match) return false;
			
			const date = moment(match.slice(1, 4).join('-'));
			const fileYear = date.isoWeekYear();
			const fileWeek = date.isoWeek();
			
			return fileYear.toString() === currentYear && fileWeek <= currentWeek;
		})
		.flatMap(file => file.tasks || []) // 正确访问每个文件的tasks
		.filter(t => t.text) // 过滤有文本的任务
		.filter(t => t.section?.subpath?.toLowerCase().includes("技术债")); // 过滤"闪念"标题下的任务
	// 3. 显示结果
	if (tasks.length > 0) {
		dv.el("p", `截止第${currentWeek}周的数据汇总`);
		dv.taskList(tasks, false);
	} else {
		dv.el("p", `📭 未找到技术债`);
	}
```
# 6. 下周聚焦

**突破点**

- 突破点：知识解构框架的可行性落地方案

**风险点**

- 项目模块运行仍存在较多问题，会影响当前目标的推进（简单记录关键影响点到[每日执行]中的“技术债”模块，待后续统一处理）

| 序号  | 原目标                                | 变更类型 | 关键进展               | 原因                 | 尝试                                                |
| --- | ---------------------------------- | ---- | ------------------ | ------------------ | ------------------------------------------------- |
| 1   | 利用[仓储知识引擎]项目完成PKMS知识模块工作流验证（[KR1]） | 冻结   | 已初步完成组件的设计，并创建对应模板 | 三大类知识笔记割裂严重，无法自然联结 | 利用“问题/情境驱动型、学习/理解驱动型、执行/操作驱动型、反思/优化驱动型”典型场景强制建立链接 |