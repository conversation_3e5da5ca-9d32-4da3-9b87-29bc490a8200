/* 标记锁定笔记 */
.dvjs-shadow-note {
    --lock-color: rgba(255,0,0,0.05);
}

/* 隐藏原始代码块 */
.dvjs-shadow-note .block-language-dataviewjs {
    display: none !important;
}

/* Shadow DOM 容器样式 */
.dvjs-shadow-container {
    position: relative;
    border: 1px solid var(--background-secondary-alt);
    border-radius: 6px;
    padding: 10px;
    margin: 15px 0;
    background-color: var(--background-primary);
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    overflow: hidden;
}

/* 锁标志样式 */
.dvjs-lock-badge {
    position: absolute;
    top: 5px;
    right: 5px;
    background: var(--background-secondary);
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 0.7em;
    color: var(--text-muted);
    z-index: 10000;
    pointer-events: none;
}