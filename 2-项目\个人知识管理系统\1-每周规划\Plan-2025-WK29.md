---
homePageLink: "[[PKMS-首页]]"
---
# 1. [[字段提示信息表#周目标描述： f37322|周目标]] 

- 为减少改善环节的流程阻碍、提升系统改善效率，于本周五18:00前完善“改善工作流”，交付以下经确认的交付物（1-4）
- 为杜绝阻碍问题积压、提升处理效率，于本周五18:00前完善“阻碍处理工作流”，交付以下经确认的交付物（4、5）

# 2. [[字段提示信息表#验收标准描述 19887e|验收标准]] 
| 交付物            | 标准/通过条件                                                                                                        | 备注  |
| -------------- | -------------------------------------------------------------------------------------------------------------- | --- |
| 「改善工作流程图」V2.0  | 1、清晰体现改善环节的所有核心动作<br>2、判断路径清晰、逻辑流畅<br>3、下周回顾阶段梳理清单时，流程中断＜2次<br>4、本周记录想法阶段，流程中断＜2次                              |     |
| 「改进待办事项」模板V2.0 | 优化后模板必须包含以下核心字段<br>（1）“缓存区”、“留观区”（以列表形式存储）<br>（2）“行动区”（以任务列表形式存储）                                              |     |
| 「洞见任务添加脚本」V2.0 | 必须满足以下核心需求：<br>1、突发灵感且需要记录时，按下快捷键即可记录，同时自动存储在「改进待办事项」“缓存区”<br>2、当突发灵感为“知识”时，自动在新标签页打开「效率工具箱」（正文若为空白，需自动添加一级标题） |     |
| 「每周计划」模板V2.1   | 改进事项统计代码<br>（1）识别有依赖关系的任务，并仅显示最靠前的任务<br>禁止包含的功能：看板包含“可执行改进项”之外的条目信息                                            |     |
| 「阻碍处理工作流程图」    | 必须满足以下要求：<br>（1）清晰体现改善环节的所有核心动作<br>（2）清晰判断路径                                                                   |     |
| 「障碍日志」模板V1.1   | 优化后的模板必须包含以下内容：<br>（1）完全满足「阻碍处理工作流」的逻辑                                                                         |     |
# 3. [[改进待办事项|改进事项]]（先梳理）
> [!dashboard]
> 
> > [!tip] 洞见/发现
> >```tasks
> >not done
> >limit 5
> >heading includes 洞见
> >description regex matches /\S/
> >hide backlink
> >sort by priority
> >filter by function \
> >const projectName = query.file.path.split("/")[1]; \
> >const targetPath = `2-项目/${projectName}/改进待办事项`; \
> >const pathMatches = task.file.path.includes(targetPath);\
> >return pathMatches;
> >sort by function \
> >const tagOrder = ["风险预警", "流程优化", "创新方案", "新增需求", "问题探索"];\
> >const matchedTag = tagOrder.find(tag => task.description.includes(tag));\
> >const order = matchedTag ? tagOrder.indexOf(matchedTag) : tagOrder.length;\
> >return order;
> >```
> 
> > [!todo] 碎片改进
> >```tasks
> >not done
> >limit 5
> >heading includes 任务
> >description regex matches /\S/
> >hide backlink
> >sort by priority
> >filter by function \
> >const projectName = query.file.path.split("/")[1]; \
> >const targetPath = `2-项目/${projectName}/改进待办事项`; \
> >const pathMatches = task.file.path.includes(targetPath);\
> >return pathMatches;
> >```

# 4. 技术债
```dataviewjs
	// 统一配置：类型和优先级设置
	const TECH_DEBT_CONFIG = {
	    types: {
	        presets: ["阻塞型", "成本型", "战略型", "无害型"],
	        get set() { return new Set(this.presets); }
	    },
	    priorities: {
	        presets: ["立即", "高", "中", "低"],
	        get set() { return new Set(this.presets); },
	        get order() { 
	            const order = {};
	            this.presets.forEach((p, i) => order[p] = i + 1);
	            return order;
	        }
	    }
	};
	
	// 获取技术债文件
	const projectName = dv.current().file.path.split("/")[1];
	const folderPath = `3-过程资产/${projectName}/技术债`;
	const allTechDebtFiles = dv.pages(`"${folderPath}"`)
	    .filter(p => 
	        p.file.name.match(/^td-\d{8}-\d{2}$/) && 
	        p.status !== "已解决" &&
	        p.file.name !== "dashboard"
	    );
	
	// 检测非预设值文件
	const hasInvalidValue = (file) => {
	    const type = file.type?.toString();
	    const priority = file.priority?.toString();
	    
	    return !TECH_DEBT_CONFIG.types.set.has(type) || 
	           !TECH_DEBT_CONFIG.priorities.set.has(priority);
	};
	
	const invalidFiles = allTechDebtFiles.filter(hasInvalidValue);
	
	// 辅助函数：获取类型显示值（仅显示预设值或提示信息）
	const getTypeDisplay = (file) => {
	    const type = file.type?.toString();
	    if (!type) return "(未设置类型)";
	    return TECH_DEBT_CONFIG.types.set.has(type) ? type : "(无效类型)";
	};
	
	// 辅助函数：获取优先级显示值（仅显示预设值或提示信息）
	const getPriorityDisplay = (file) => {
	    const priority = file.priority?.toString();
	    if (!priority) return "(未设置优先级)";
	    return TECH_DEBT_CONFIG.priorities.set.has(priority) ? priority : "(无效优先级)";
	};
	
	// 显示结果
	if (invalidFiles.length > 0) {
	    // 输出非预设值文件  
	    const listItems = invalidFiles.map(file => {
	        const displayName = file.aliases || file.file.name;
	        const typeDisplay = getTypeDisplay(file);
	        const priorityDisplay = getPriorityDisplay(file);
	        return `[[${file.file.name}|${displayName}]] - ${typeDisplay} - ${priorityDisplay}`;
	    });
	    
	    // 使用单个Markdown列表输出
	    dv.paragraph(listItems);
	} else {
	    // 如果没有非预设值文件，则按原逻辑处理预设值文件
	    const validFiles = allTechDebtFiles;
	    // 确定目标类型
	    let targetType = null;
	    for (const type of TECH_DEBT_CONFIG.types.presets) {
	        if (validFiles.some(p => p.type === type)) {
	            targetType = type;
	            break;
	        }
	    }
	    if (!targetType) {
	        dv.paragraph("⚠️ 没有待处理的技术债");
	    } else {
	        // 过滤并排序文件
	        const filteredFiles = validFiles
	            .filter(p => p.type === targetType)
	            .sort(p => TECH_DEBT_CONFIG.priorities.order[p.Priority] || 999);
	        
	        if (filteredFiles.length === 0) {
	            dv.paragraph(`⚠️ 没有${targetType}类型的技术债`);
	        } else {          
	            // 创建优先级分组
	            const groupedByPriority = {};
	            filteredFiles.forEach(file => {
	                const priority = file.priority || "未设置";
	                if (!groupedByPriority[priority]) {
	                    groupedByPriority[priority] = [];
	                }
	                groupedByPriority[priority].push(file);
	            });
	            
	            // 收集所有输出行
	            const outputLines = [];
	            TECH_DEBT_CONFIG.priorities.presets.forEach(priority => {
	                if (groupedByPriority[priority]) {
	                    groupedByPriority[priority].forEach(file => {
	                        const displayName = file.aliases || file.file.name;
	                        outputLines.push(
	                            `[[${file.file.name}|${displayName}]] - ${file.type} - ${priority}`
	                        );
	                    });
	                }
	            });
	            // 使用单个Markdown列表输出所有内容
	            dv.paragraph(outputLines);
	        }
	    }
	}
```
# 5. 任务拆解

- [x] 创建「改善工作流程图」 ⏳ 2025-07-15 ✅ 2025-07-16
- [x] 按照「改善工作流程图」梳理「改进待办事项」清单，并验证优化工作流 ⏳ 2025-07-15 ✅ 2025-07-16
- [x] 创建「改进待办事项」模板 ⏳ 2025-07-16 ✅ 2025-07-16
- [ ] 重构「洞见任务添加脚本」JS代码 ⏳ 2025-07-16
- [ ] 删除「每周计划」”改进事项“中”洞见/发现“代码，并重构”碎片改进“代码（取消看板形式）
- [ ] 优化「每周计划」模板
- [ ] 探索阻碍处理的工作流
- [ ] 绘制「阻碍处理工作流程图」
- [ ] 〖改善〗优化「障碍日志创建」脚本及说明文档
- [ ] 优化「障碍日志」模板
- [ ] 