---
created_Date: 2025-07-07
aliases:
  - 由于无法明确阻碍记录原则，导致在创建有关联的阻碍时无从下手，进而影响了后续问题的解决和复盘
type: 认知断层
status: 新建
relation:
  - "[[blocker-20250706-01]]"
  - "[[blocker-20250707-02]]"
---
# 1. 基础信息

- 发生场景：记录阻碍
- 核心问题：新创建的阻碍与历史阻碍有因果关系时，应该如何记录？
- 关键影响：新阻碍被遗漏，旧阻碍被覆盖，导致问题演进路径丢失，不利于后问题解决和复盘

# 2. 关键记录

| 日期&时间            | 行动               | 结果                                | 状态  | 发现/洞见                                                                                                                                                                              |
| ---------------- | ---------------- | --------------------------------- | --- | ---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| 2025-07-07 20:22 | 询问deepseek具体解决方案 | 针对连续出现的、具有因果关系的阻碍，既需要分开记录，又需要建立关联 | 验证中 | 阻碍记录七大黄金原则：<br>（1）原子性原则<br>（2）现象-影响锚定原则（可观测的现象+量化影响--尽量）<br>（3）解决方案中立原则（记录时不预设解决方案）<br>（4）动态关联原则<br>（5）可验证性原则（每条阻碍需定义验证指标）<br>（6）迭代进化原则<br>（7）知识沉淀原则（关闭阻碍时记录根因分析结论、失效解决方案归档至团队知识库） |

# 3. 解决方案

| 日期  | 根因分析 | 行动方案 | 残留风险 | 后续行动 | 备注  |
| --- | ---- | ---- | ---- | ---- | --- |
|     |      |      |      |      |     |
