# 1. [[字段提示信息表#闪念描述：[触发场景] + [核心方案] + [价值]​​ ec14e2|洞见/发现]]

- [ ] 随着自动化脚本的增加快捷键数量随之增加，同时也增加了使用难度
- [ ] 使用JS脚本创建阻碍时，无法通过”ESC“取消创建动作 #BUG
- [ ] 在完成周改善措施后，如何处理改善项，才能使其闭环 #问题探索 
- [ ] ”改善代办“与”闪念“管理的方式十分相似，但是发现的BUG或者其他类型的任务应该怎么办 #问题探索 
- [ ] 通过将「每日执行」组件中的”洞见/发现“模块升级为「改进待办列表」进行独立管理，可以提升想法产生场景的覆盖率，避免其他环节出现想法时的卡顿问题 #流程优化 
- [ ] 通过在”回顾“阶段选择1-2项高价值且紧急的改善项，将其纳入”改善措施“、在”规划“阶段选择高优先级的改善项，将其纳入本周迭代、在”评审“阶段对改善结果进行价值评估，可以使「改进待办列表」中的改善项形成闭环，提升改善的质量与效率 #流程优化 
- [ ] 阻碍记录中“关键行动”表格的“行动”、“结果”、“洞见/发现”字段没有逻辑连贯性，不知道如何填写对应内容？ #问题探索 
- [ ] 通过在阻碍日志模板中添加“发生次数”字段，可以为阻碍排序提供参考依据，为后续的分级处理提供数据支撑 #流程优化 
- [ ] 通过在dashboard中添加折线图并查看一段时间内的阻碍新增情况，可以了解项目的健康状况 #流程优化 
- [ ] 自定义CSS代码并在笔记属性添加特定的值，以提高dataview查询结果的查看效率，避免直接暴露源代码 #流程优化 
- [ ] 通过修改「dataview-table-style.css」使表格宽度自适应文字，避免日期列内容换行，可以提高表格信息查看侠效率 #新增需求 
- [ ] 使用JS脚本创建阻碍时，文件名称对应的时间与笔记属性的创建时间不一致 #BUG 
- [ ] 当每周回顾的“流程改善”模块下的表格内容为空时，“改善回顾”统计模块依然会统计相关空表格的数据 #新增需求 
- [x] 在每周评审阶段进行查看已完成成果清单时，通过对成果列表内容按照名称排序，可以更清晰的进行信息梳理，提高工作效率 #新增需求 ✅ 2025-07-06
- [x] 在查看每日执行、每周评审、每周回顾的看板信息时，无意间的点击动作会导致查询代码显漏出来，影响工作节奏 #风险预警 ✅ 2025-07-06
- [ ] 在记录阻碍“关键行动”表格中的时间时，通过“微信输入法”自带的时间选择器（拼写“时间”时出现），可以提高输入效率 #创新方案 
- [ ] 闪念的产生并不一定是在执行任务期间，是否需要独立管理 #问题探索 
- [ ] 在记录有关联的阻碍时，手动建立链接效率低、容易遗漏，导致产生额外的工作负担和心理负担 #流程优化 
- [ ] 在记录阻碍时，若新创建的阻碍与历史阻碍属于同一问题域，但是发生的场景不同，应该如何处理？ #问题探索 
- [ ] 在填写阻碍对应的关键行动时，发现每个行动的状态对问题演进路径似乎没有直接的帮助？ #问题探索 
- [x] 在“3-过程资产/项目名称”文件夹下为障碍创建专门文件夹⟦阻碍⟧，可以提高阻碍文件的管理效率 #新增需求 ✅ 2025-07-06
- [x] 在使用js脚本自动创建“障碍”时，通过将文件的status属性设置为默认“新建”状态，可以提升模板的填写效率 #新增需求
- [x] 在使用js脚本自动创建“技术债”时，通过将文件的status属性设置为默认“待处理”状态，可以提升模板的填写效率 #新增需求 ✅ 2025-07-03
- [ ] 在项目执行期间，突然发现一个BUG，应该如何处理？ #问题探索 
- [-] 删除【每周计划】中的“阻碍/技术债”并将其嵌入（障碍日志 + dataview）“任务看板”，同时拆分“闪念/输出”为2个标题，可以简化组件内容结构并减小组件污染 #新增需求
- [ ] 在记录障碍日志时，是否所有的障碍都需要记录 #问题探索 
- [ ] 当模板被迭代优化后，迭代的具体细节应该如何记录？记录在什么位置更合适 #问题探索 
- [-] 在嵌入block的相关内容时，通过自定义CSS隐藏其后的“链接”图标，可以是内容更干净、整洁 #新增需求
- [-] 在“每周评审”阶段，通过分别统计汇总未完成的闪念和技术债，可以指导下周计划 #流程优化
- [-] 当上周存在未完成任务时，是否需要在【每日执行】的“任务安排”模块统计历史未完成任务至未规划中？（每个人物或任务集都对应着交付成果，每周复盘、回顾阶段都会闭环跟踪） #问题探索
- [ ] 在查看“任务安排”中的任务信息时，是否需要显示每条任务的源文件链接？ #问题探索 
- [ ] 在查看项目首页时，通过自定义CSS样式缩小查询表格高度，可以提高信息查看效率并较小页面空间布局 #新增需求
- [x] 当开始新的一周计划时，通过技术手段获取距离当前最近一周且已开展果工作的“复盘”信息，避免信息断裂 #新增需求 ✅ 2025-06-23
- [ ] 在执行每日任务期间，若当日任务为持续性任务或一日不能完成的任务，那么应该如何记录和安排 #问题探索 
- [x] 在「每周复盘」阶段进行价值描述时，不准确的表达无法提现“成果”对KR的影响 #风险预警 ✅ 2025-06-28
- [-] 在使用dataviewjs统计“阻碍 ”或者“技术债”时，通过技术手段在条目前加上序号，可以方便统计和查看 #新增需求
- [-] 在查看表格是，按照`dataview`的默认表格样式渲染手动创建的表格，可以使查看的内容更美观、舒服 #新增需求
- [x] 在查看表格是，通过自定义CSS片段，可以优化表格前后空行问题 #新增需求 ✅ 2025-06-15
- [ ] 在obsidian中新建、重新打开文件或重启软件时，鼠标光标的默认位置会影响文件的查看（特别是开头为代码块），不利于查看和编辑文档 #风险预警 
- [x] 在[每日执行]查看“任务安排”时，模板标题序号的变动会导致任务无法被筛选（代码失效） #风险预警 ✅ 2025-06-15
- [x] 在编写闪念、技术债、验收标准等内容时，通过增加关键词信息提示功能，可以优化掉重复翻看模板的动作 #流程优化 ✅ 2025-06-15
- [ ] 在excallidraw绘图时，默认的字号类型不能覆盖全部的使用场景，导致部分场景下的图形布局杂乱或畸变 #风险预警 
- [-] 在查看“任务安排”内容时，通过技术手段将任务（已完成/未完成）按照创建的顺序排序，可以更好的还原任务之间思考逻辑 #新增需求
- [ ] 每周在进行复盘时（“成果验收”环节），通过记录“关键成果”和“​​计划外成果”（未计划但实际产生价值的），可以避免成果和进度信息的缺失 #新增需求 
- [x] 在进行闪念描述时，通过使用万能/通用公式，可以提升描述的准确性，避免关键信息遗漏 #流程优化 ✅ 2025-06-15
- [x] 在完成闪念处理时，通过在对应条目后增加完成日期，可以对“计划外成果”进行更准确的复盘 #新增需求 ✅ 2025-06-15
- [x] 在进行每周复盘时，“成果验收”与“KR进度”中的举证重复，导致组件内容结构、逻辑混乱 #风险预警 ✅ 2025-06-15
- [x] 在进行每周复盘时，KR进度描述不清晰，导致后续目标执行、调整难度增加 #风险预警 ✅ 2025-07-06
- [x] 在进行“每周复盘”时，通过“看板工具”统计本周输出和闪念，可以提高复盘效率以及简化[每周复盘]组件的内容结构 #流程优化 ✅ 2025-06-15
- [x] 在使用“看板工具”时，通过在“成果验收”标题处增加链接提醒，可以增加使用效率 #流程优化 ✅ 2025-06-15
- [x] 在使用“看板工具”查看闪念信息时，通过技术手段按照“风险预警-->流程优化-->创新方案-->需求响应-->问题探索”的顺序排序，可以提高信息查看效率 #流程优化 ✅ 2025-06-15
- [ ] 在项目推进期间，因周目标设置过大、不清晰或者项目执行偏差，导致下阶段的（复盘、回顾）工作无法顺利进行 #风险预警 
- [x] 在“每周复盘”、“每周回顾”阶段，因组件内容结构逻辑不清晰，导致无法顺利开展相关复盘和回顾工作 #风险预警 ✅ 2025-07-06
- [x] 在查看[每日执行]页面内容时，将“技术债”与“阻碍”合并并以标签的方式进行管理，可以避免“页面内容冗杂”和“内容逻辑不顺” #新增需求 ✅ 2025-06-15
- [x] 在查看[每日执行]页面内容时，移动“目标异动”到其他文档，可以避免“页面内容冗杂”和“内容逻辑不顺” #新增需求 ✅ 2025-06-15
- [x] 在查看[每日执行]页面内容时，将“闪念”与“输出”合并，可以避免“页面内容冗杂”和“内容逻辑不顺” #新增需求 ✅ 2025-06-15
- [x] 在知识解构时，将“阻碍日志”和“输出”作为“过程资产”进行集中管理，可以避免文档混乱、搜索困难 #新增需求 ✅ 2025-07-06
- [x] 在进行闪念记录时，通过问题引导（是否为实现短期目标而牺牲长期质量或可维护性的妥协决策或疏漏？​​），可以解决技术债与灵感混淆的问题 #流程优化 ✅ 2025-06-15
- [x] 通过CSS片段，重新调整[每日指定]任务看板背景色、布局，可以使任务面板更简洁、美观 #新增需求 ✅ 2025-06-15
- [ ] 在执行任务期间，发现并解决了一个坑点，是否需要立刻记录下来？ #问题探索
- [ ] 在项目推进（迭代）期间，KR进度中已关联的“证据”被优化或删除，原始文件是否需要同时删除？ #问题探索 
- [x] 在成果验收和KR进度评估的举证阶段，如何可以快速定位当前迭代中的产出？ #问题探索 ✅ 2025-06-15
- [ ] 在新建[每日执行]、[每周规划]、[每周复盘]、[每周回顾]文件时，通过JS脚本和模板功能，可以简化文件创建和文件归档动作 #流程优化
- [ ] 在新建[输出]时，通过JS脚本，可以实现自动创建文件、创建双链的功能并简化文件创建和文件归档动作 #流程优化 
- [x] 通过CSS调整表格的布局（首行/首列水平、垂直居中；单元格内容垂直居中）可以使表格更美观 #新增需求 ✅ 2025-06-25
- [x] 通过简化[每周复盘]中的内容结构，可以降低认知负担并提升复盘效率 #流程优化 ✅ 2025-06-25
- [-] 在进行知识消费时，如何以更好的方式显示陈述性知识、程序性知识、条件性知识之间的关系（更好的知识图谱） ？ #问题探索
- [x] 突发灵感时，通过在每日执行文件中添加[灵感想法]并以任务方式存储，并每周复盘中使用dataview动态统计历史累计的灵感，可以提高闪念处理的效率和质量 #流程优化 ✅ 2025-06-04
- [-] 若突发的灵感与当前目标相关且影响当周目标的完成，调整并添加对应的新任务，优先完成新灵感任务（目标）或将任务安排在本周末尾/下周提高优先级，可以提高本周目标完成质量 #流程优化
- [x] 若突发的灵感与当前目标之间无直接影响，综合下周目标综合考虑优先级顺序，可以提升整体工作效率 #流程优化
- [x] 每日执行的任务安排模块可以只简单统计当日代办（所有状态）+ 未规划任务即可（当日不能完成任务在当日工作结束前重新规划），可以避免重复跳转至[每周计划]页面进行任务安排，同时避免历史文件任务模块空白（无信息）的问题 #流程优化 ✅ 2025-06-04
- [ ] 在进行知识版本管理时，通过技术手段使知识组件笔记属性[update_Date]在修改完文件后自动更新，可以直观了解最新的修改状态 #新增需求 
- [-] 在知识解构时，如何编写核心概念笔记（陈述性知识）、流程方法（程序性知识）笔记、条件性知识笔记？ #问题探索 
- [ ] 如何在不同组件之间自然的相互跳转？ #问题探索 
- [x] 通过在[每周规划]、[每日执行]、[每周复盘]、[每周回顾]中添加笔记属性[homePageLink]，可以提高组件跳转的灵活性 #流程优化  ✅ 2025-06-03

# 2. 可执行任务

- [ ] 按照“由于 「具体现象」，导致「直接后果」，进而造成「深层影响」”句式重新梳理所有阻碍描述
- [ ] 修复「每日执行」“阻碍”代码结果显示异常问题
- [x] 修正「dashboard」代码，使结果按照修改时间排序 ✅ 2025-07-10
- [ ] 将文件[[Do-2025-06-15]]、[[Do-2025-06-14]]、[[Do-2025-06-12]]、[[Do-2025-06-08]]中的技术债或阻碍按照最新的管理方式存储，并删除文件中冗余的内容
- [x] 优化「每周计划」”改进事项“模块代码，使其仅展示优先级最高的前5条信息
- [ ] 梳理”洞见/发现“，为其添加优先级属性
- [x] 优化「每周计划」”改进事项“模块代码，增加”优先级“由高到低排序功能
- [ ] 

