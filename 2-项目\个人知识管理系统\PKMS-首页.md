---
startDate: 2025-05-26
endDate: 2025-06-30
---
# 1. OKR设定

- O：搭建个人知识管理系统
- KR1：探索PKMS系统的基本内容结构（组件）
- KR2：探索PKMS系统的核心工作流

# 2. 项目进度
```dataviewjs
	// 定义 getWeekNumber 函数
	function getWeekNumber(date) {
	    const d = new Date(date);
	    d.setHours(0, 0, 0, 0);
	    d.setDate(d.getDate() + 3 - (d.getDay() + 6) % 7);
	    const week1 = new Date(d.getFullYear(), 0, 4);
	    return 1 + Math.round(((d - week1) / 86400000 - 3 + (week1.getDay() + 6) % 7) / 7);
	}
	
	// 获取当前文件的路径信息，提取项目名称
	const currentFile = dv.current().file;
	const currentPath = currentFile.path;
	const projectName = currentPath.split("/")[1].trim();
	
	// 获取当前日期并计算年份和上周周数
	const now = new Date();
	let year = now.getFullYear();
	let weekNumber = getWeekNumber(now) - 1;
	
	// 初始化查找结果
	let krContent = null;
	let foundWeek;
	let foundYear;
	
	// 尝试查找最近的周报（最多回溯4周）
	for (let i = 0; i < 4; i++) {
	    // 处理跨年情况（如果当前是第一周，则上周是去年的最后一周）
	    if (weekNumber < 1) {
	        year--;
	        weekNumber = 52; // ISO周数最大为52或53周
	    }
	    
	    // 动态生成文件名
	    const dynamicFilename = `Replay-${year}-WK${weekNumber.toString().padStart(2, '0')}.md`;
	    const path = `2-项目/${projectName}/3-每周复盘/${dynamicFilename}`;
	    const file = app.vault.getAbstractFileByPath(path);
	    
	    if (file) {
	        // 读取并解析文件
	        const content = await dv.io.load(path);
	        const headingRegex = /(?:^|\n)#{1,6}\s*.*?KR进度.*?(?:\n|$)([\s\S]*?)(?=\n?#{1,6}\s|$)/i;
	        const match = content.match(headingRegex);
	        
	        if (match) {
	            krContent = match[1].trim();
	            foundWeek = weekNumber;
	            foundYear = year;
	            break; // 找到有效内容，跳出循环
	        }
	    }
	    
	    // 准备回溯到更早的一周
	    weekNumber--;
	}
	
	// 输出结果
	if (krContent) {
	    dv.paragraph(krContent);
	} else {
	    dv.el("p", "未找到近期项目周报");
	}
```
# 3. 行动变更

| 变更日期       | 计划/行动名称           | 决策类型 | 决策依据                                            | 决策失误原因                                             | 关键证据                          | 新行动                           |
| ---------- | ----------------- | ---- | ----------------------------------------------- | -------------------------------------------------- | ----------------------------- | ----------------------------- |
| 2025-07-09 | 「每周计划」阻碍回顾范围探索    | 删除   | 个人工作场景下，「首次应急处理」+「回顾阶段系统分析」两阶段模型即可完成阻碍的处理，发挥其作用 | 阻碍处理的核心目的理解不准确，未对周评审环节造成严重影响，但导致周回顾环节无法进行改善分析与方案制定 | [[blocker-20250707-02]]@洞见/发现 | 建立「改进待办事项」工作流，以补全计划、闭环系统性改善流程 |
| 2025-07-10 | 组件看板优化（代码优化、误触问题） | 冻结   | 当前技术手段无法实现                                      |                                                    |                               |                               |
