---
created_Date: 2025-06-06
aliases:
  - 由于无法区分阻碍、技术债、闪念之间的差异与联系，导致记录问题时无从下手，进而影响了问题的可见性、优先级判断、根因分析以及团队的持续改进能力
relation:
  - 每日执行
type: 流程机制
status: 新建
---
# 1. 基础信息

- 发生场景：遇到问题时
- 核心问题：不知道如何区分阻碍、技术债、闪念
- 关键影响：真正紧急的阻碍可能被淹没在大量的技术债和闪念中，无法被及时识别和关注；资源错配；问题的根本原因被模糊

# 2. 关键记录

| 时间               | 行动        | 结果  | 状态  | 发现/洞见                           |
| ---------------- | --------- | --- | --- | ------------------------------- |
| 2025-06-11 14:00 | 在临时位置简单记录 | 成功  | 验证中 | 技术债是指实现短期目标而牺牲长期质量或可维护性的妥协决策或疏漏 |

# 3. 解决方案

| 日期  | 根因分析 | 行动方案 | 残留风险 | 后续行动 | 备注  |
| --- | ---- | ---- | ---- | ---- | --- |
|     |      |      |      |      |     |
