{"disableDoubleClickTextEditing": false, "folder": "", "cropFolder": "", "annotateFolder": "", "embedUseExcalidrawFolder": false, "templateFilePath": "Excalidraw/Template.excalidraw", "scriptFolderPath": "0-辅助/<PERSON><PERSON><PERSON>", "fontAssetsPath": "Excalidraw/CJK Fonts", "loadChineseFonts": false, "loadJapaneseFonts": false, "loadKoreanFonts": false, "compress": true, "decompressForMDView": false, "onceOffCompressFlagReset": true, "onceOffGPTVersionReset": true, "autosave": true, "autosaveIntervalDesktop": 60000, "autosaveIntervalMobile": 30000, "drawingFilenamePrefix": "Drawing ", "drawingEmbedPrefixWithFilename": true, "drawingFilnameEmbedPostfix": " ", "drawingFilenameDateTime": "YYYY-MM-DD HH.mm.ss", "useExcalidrawExtension": true, "cropSuffix": "", "cropPrefix": "cropped_", "annotateSuffix": "", "annotatePrefix": "annotated_", "annotatePreserveSize": false, "previewImageType": "SVGIMG", "renderingConcurrency": 3, "allowImageCache": true, "allowImageCacheInScene": true, "displayExportedImageIfAvailable": false, "previewMatchObsidianTheme": false, "width": "400", "height": "", "overrideObsidianFontSize": false, "dynamicStyling": "colorful", "isLeftHanded": false, "iframeMatchExcalidrawTheme": true, "matchTheme": false, "matchThemeAlways": false, "matchThemeTrigger": false, "defaultMode": "normal", "defaultPenMode": "never", "penModeDoubleTapEraser": true, "penModeSingleFingerPanning": true, "penModeCrosshairVisible": true, "renderImageInMarkdownReadingMode": false, "renderImageInHoverPreviewForMDNotes": false, "renderImageInMarkdownToPDF": false, "allowPinchZoom": false, "allowWheelZoom": false, "zoomToFitOnOpen": true, "zoomToFitOnResize": true, "zoomToFitMaxLevel": 2, "linkPrefix": "📍", "urlPrefix": "🌐", "parseTODO": false, "todo": "☐", "done": "🗹", "hoverPreviewWithoutCTRL": false, "linkOpacity": 1, "openInAdjacentPane": true, "showSecondOrderLinks": true, "focusOnFileTab": true, "openInMainWorkspace": true, "showLinkBrackets": true, "allowCtrlClick": true, "forceWrap": false, "pageTransclusionCharLimit": 200, "wordWrappingDefault": 0, "removeTransclusionQuoteSigns": true, "iframelyAllowed": true, "pngExportScale": 1, "exportWithTheme": true, "exportWithBackground": true, "exportPaddingSVG": 10, "exportEmbedScene": false, "keepInSync": false, "autoexportSVG": false, "autoexportPNG": false, "autoExportLightAndDark": false, "autoexportExcalidraw": false, "embedType": "excalidraw", "embedMarkdownCommentLinks": true, "embedWikiLink": true, "syncExcalidraw": false, "experimentalFileType": false, "experimentalFileTag": "✏️", "experimentalLivePreview": true, "fadeOutExcalidrawMarkup": false, "loadPropertySuggestions": true, "experimentalEnableFourthFont": false, "experimantalFourthFont": "<PERSON>", "addDummyTextElement": false, "zoteroCompatibility": false, "fieldSuggester": true, "compatibilityMode": false, "drawingOpenCount": 0, "library": "deprecated", "library2": {"type": "excalidrawlib", "version": 2, "source": "https://github.com/zsviczian/obsidian-excalidraw-plugin/releases/tag/2.12.4", "libraryItems": [{"id": "-hvvtsU1qnblK0ok55i-Q", "status": "unpublished", "elements": [{"id": "cbaj1yVKkyCCVcyv5MLt1", "type": "arrow", "x": 477.39866921255435, "y": 1577.3420367474125, "width": 271.7647058823509, "height": 4.6588116814108105, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "#1e1e1e", "fillStyle": "hachure", "strokeWidth": 0.5, "strokeStyle": "solid", "roughness": 0, "opacity": 100, "groupIds": ["GDPPThHX-QClbAL8G58hG"], "frameId": null, "index": "b38V", "roundness": null, "seed": 1580298275, "version": 477, "versionNonce": 93090349, "isDeleted": false, "boundElements": [], "updated": 1746780058612, "link": null, "locked": false, "points": [[0, 0], [-271.7647058823509, -4.6588116814108105]], "lastCommittedPoint": null, "startBinding": null, "endBinding": null, "startArrowhead": null, "endArrowhead": "triangle", "elbowed": false}, {"id": "Qn5lUl8CVCV_GJIYP8A7l", "type": "arrow", "x": 477.3986686955517, "y": 1577.3420370660017, "width": 40.470545151654505, "height": 0, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "#1e1e1e", "fillStyle": "hachure", "strokeWidth": 0.5, "strokeStyle": "solid", "roughness": 0, "opacity": 100, "groupIds": ["GDPPThHX-QClbAL8G58hG"], "frameId": null, "index": "b39", "roundness": null, "seed": 1992076397, "version": 1234, "versionNonce": 497622819, "isDeleted": false, "boundElements": [], "updated": 1746780041490, "link": null, "locked": false, "points": [[0, 0], [40.470545151654505, 0]], "lastCommittedPoint": null, "startBinding": null, "endBinding": null, "startArrowhead": null, "endArrowhead": "triangle", "elbowed": false}], "created": 1746780073005}, {"id": "dFNkUJ-BJo9_usDLZh9FI", "status": "unpublished", "elements": [{"id": "63OD0-uA8B8MTsA0uj5pm", "type": "line", "x": 565.5376245543882, "y": -66.76473229091404, "width": 0, "height": 119.38744739869574, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "#1e1e1e", "fillStyle": "solid", "strokeWidth": 0.5, "strokeStyle": "solid", "roughness": 0, "opacity": 100, "groupIds": ["AKtqVfIQ_owSHxmnam_Y6"], "frameId": null, "index": "b1Bn", "roundness": {"type": 2}, "seed": 552410096, "version": 813, "versionNonce": 55363876, "isDeleted": false, "boundElements": [], "updated": 1746264069596, "link": null, "locked": false, "points": [[0, 0], [0, 119.38744739869574]], "lastCommittedPoint": null, "startBinding": null, "endBinding": null, "startArrowhead": null, "endArrowhead": null, "polygon": false}, {"id": "HTpkxaryDkJX6r8-k81rz", "type": "line", "x": 565.5376245543882, "y": -67.28382011596686, "width": 5.7098472685254595, "height": 0, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "#1e1e1e", "fillStyle": "solid", "strokeWidth": 0.5, "strokeStyle": "solid", "roughness": 0, "opacity": 100, "groupIds": ["AKtqVfIQ_owSHxmnam_Y6"], "frameId": null, "index": "b1Bp", "roundness": {"type": 2}, "seed": 247733744, "version": 758, "versionNonce": 1000000284, "isDeleted": false, "boundElements": [], "updated": 1746264069596, "link": null, "locked": false, "points": [[0, 0], [5.7098472685254595, 0]], "lastCommittedPoint": null, "startBinding": null, "endBinding": null, "startArrowhead": null, "endArrowhead": null, "polygon": false}, {"id": "f495Pa_t_3w9757hrSwW_", "type": "line", "x": 565.5376245879711, "y": 53.141802999999996, "width": 5.7098472685254595, "height": 0, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "#1e1e1e", "fillStyle": "solid", "strokeWidth": 0.5, "strokeStyle": "solid", "roughness": 0, "opacity": 100, "groupIds": ["AKtqVfIQ_owSHxmnam_Y6"], "frameId": null, "index": "b1Bt", "roundness": {"type": 2}, "seed": 1852497904, "version": 765, "versionNonce": 2023752868, "isDeleted": false, "boundElements": [], "updated": 1746264069596, "link": null, "locked": false, "points": [[0, 0], [5.7098472685254595, 0]], "lastCommittedPoint": null, "startBinding": null, "endBinding": null, "startArrowhead": null, "endArrowhead": null, "polygon": false}, {"id": "18xP22Dc_Aois-mq4hwAZ", "type": "line", "x": 559.8277776552736, "y": -7.071008759480222, "width": 5.7098472685254595, "height": 0, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "#1e1e1e", "fillStyle": "solid", "strokeWidth": 0.5, "strokeStyle": "solid", "roughness": 0, "opacity": 100, "groupIds": ["AKtqVfIQ_owSHxmnam_Y6"], "frameId": null, "index": "b1Bx", "roundness": {"type": 2}, "seed": 1877565936, "version": 773, "versionNonce": 1691027356, "isDeleted": false, "boundElements": [], "updated": 1746264069596, "link": null, "locked": false, "points": [[0, 0], [5.7098472685254595, 0]], "lastCommittedPoint": null, "startBinding": null, "endBinding": null, "startArrowhead": null, "endArrowhead": null, "polygon": false}], "created": 1746776661041}, {"id": "WmqBLdeN7KiLmMayXz6QN", "status": "unpublished", "elements": [{"type": "line", "version": 1103, "versionNonce": 568427804, "isDeleted": false, "id": "9L4l3kjtXHXVzfFBnoy2X", "fillStyle": "hachure", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "angle": 0, "x": -649.5092238691286, "y": -416.44566452172853, "strokeColor": "#1e1e1e", "backgroundColor": "#1e1e1e", "width": 100, "height": 80, "seed": 315649988, "groupIds": [], "boundElements": [], "updated": 1746264069596, "link": null, "locked": false, "startBinding": null, "endBinding": null, "lastCommittedPoint": null, "startArrowhead": null, "endArrowhead": null, "points": [[0, 0], [0, 40], [60, 40], [60.00000000000001, 60.00000000000001], [100, 19.999999999999993], [60, -19.999999999999996], [60, 3.552713678800501e-15], [0, 0]], "index": "aF", "frameId": null, "roundness": null, "polygon": false}], "created": 1746776644327}]}, "imageElementNotice": true, "mdSVGwidth": 500, "mdSVGmaxHeight": 800, "mdFont": "<PERSON>", "mdFontColor": "Black", "mdBorderColor": "Black", "mdCSS": "", "scriptEngineSettings": {"Downloaded/Mindmap format": {"MindMap Format": {"value": "Excalidraw/MindMap Format", "description": "This is prepared for the namespace of MindMap Format and does not need to be modified"}, "default gap": {"value": 10, "description": "Interval size of element"}, "curve length": {"value": 40, "description": "The length of the curve part in the mind map line"}, "length between element and line": {"value": 50, "description": "The distance between the tail of the connection and the connecting elements of the mind map"}}, "Downloaded/Auto Layout": {"Layout Options JSON": {"height": "450px", "value": "{\n      \"org.eclipse.elk.layered.crossingMinimization.semiInteractive\": \"true\",\n      \"org.eclipse.elk.layered.considerModelOrder.components\": \"FORCE_MODEL_ORDER\"\n}", "description": "You can use layout options to configure the layout algorithm. A list of all options and further details of their exact effects is available in <a href=\"http://www.eclipse.org/elk/reference.html\" rel=\"nofollow\">ELK's documentation</a>."}}, "Downloaded/Fixed spacing": {"Prompt for spacing?": true, "Default spacing": {"value": 10, "description": "Fixed horizontal spacing between elements"}, "Remember last spacing?": false}, "Downloaded/Normalize Selected Arrows": {"Gap": {"value": 8, "description": "The value of the gap between the connection line and the element, which must be greater than 0. If you want the connector to be next to the element, set it to 1."}}, "Mindmap format": {"MindMap Format": {"value": "Excalidraw/MindMap Format", "description": "This is prepared for the namespace of MindMap Format and does not need to be modified"}, "default gap": {"value": 10, "description": "Interval size of element"}, "curve length": {"value": 40, "description": "The length of the curve part in the mind map line"}, "length between element and line": {"value": 50, "description": "The distance between the tail of the connection and the connecting elements of the mind map"}}, "Fixed spacing": {"Prompt for spacing?": true, "Default spacing": {"value": 10, "description": "Fixed horizontal spacing between elements"}, "Remember last spacing?": false}}, "defaultTrayMode": true, "previousRelease": "2.12.4", "showReleaseNotes": true, "showNewVersionNotification": true, "latexBoilerplate": "\\color{blue}", "latexPreambleLocation": "preamble.sty", "taskboneEnabled": false, "taskboneAPIkey": "", "pinnedScripts": [], "customPens": [{"type": "default", "freedrawOnly": false, "strokeColor": "#000000", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 0, "roughness": 0, "penOptions": {"highlighter": false, "constantPressure": false, "hasOutline": false, "outlineWidth": 1, "options": {"thinning": 0.6, "smoothing": 0.5, "streamline": 0.5, "easing": "easeOutSine", "start": {"cap": true, "taper": 0, "easing": "linear"}, "end": {"cap": true, "taper": 0, "easing": "linear"}}}}, {"type": "highlighter", "freedrawOnly": true, "strokeColor": "#FFC47C", "backgroundColor": "#FFC47C", "fillStyle": "solid", "strokeWidth": 2, "roughness": null, "penOptions": {"highlighter": true, "constantPressure": true, "hasOutline": true, "outlineWidth": 4, "options": {"thinning": 1, "smoothing": 0.5, "streamline": 0.5, "easing": "linear", "start": {"taper": 0, "cap": true, "easing": "linear"}, "end": {"taper": 0, "cap": true, "easing": "linear"}}}}, {"type": "finetip", "freedrawOnly": false, "strokeColor": "#3E6F8D", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 0.5, "roughness": 0, "penOptions": {"highlighter": false, "hasOutline": false, "outlineWidth": 1, "constantPressure": true, "options": {"smoothing": 0.4, "thinning": -0.5, "streamline": 0.4, "easing": "linear", "start": {"taper": 5, "cap": false, "easing": "linear"}, "end": {"taper": 5, "cap": false, "easing": "linear"}}}}, {"type": "fountain", "freedrawOnly": false, "strokeColor": "#000000", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 2, "roughness": 0, "penOptions": {"highlighter": false, "constantPressure": false, "hasOutline": false, "outlineWidth": 1, "options": {"smoothing": 0.2, "thinning": 0.6, "streamline": 0.2, "easing": "easeInOutSine", "start": {"taper": 150, "cap": true, "easing": "linear"}, "end": {"taper": 1, "cap": true, "easing": "linear"}}}}, {"type": "marker", "freedrawOnly": true, "strokeColor": "#B83E3E", "backgroundColor": "#FF7C7C", "fillStyle": "dashed", "strokeWidth": 2, "roughness": 3, "penOptions": {"highlighter": false, "constantPressure": true, "hasOutline": true, "outlineWidth": 4, "options": {"thinning": 1, "smoothing": 0.5, "streamline": 0.5, "easing": "linear", "start": {"taper": 0, "cap": true, "easing": "linear"}, "end": {"taper": 0, "cap": true, "easing": "linear"}}}}, {"type": "thick-thin", "freedrawOnly": true, "strokeColor": "#CECDCC", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 0, "roughness": null, "penOptions": {"highlighter": true, "constantPressure": true, "hasOutline": false, "outlineWidth": 1, "options": {"thinning": 1, "smoothing": 0.5, "streamline": 0.5, "easing": "linear", "start": {"taper": 0, "cap": true, "easing": "linear"}, "end": {"cap": true, "taper": true, "easing": "linear"}}}}, {"type": "thin-thick-thin", "freedrawOnly": true, "strokeColor": "#CECDCC", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 0, "roughness": null, "penOptions": {"highlighter": true, "constantPressure": true, "hasOutline": false, "outlineWidth": 1, "options": {"thinning": 1, "smoothing": 0.5, "streamline": 0.5, "easing": "linear", "start": {"cap": true, "taper": true, "easing": "linear"}, "end": {"cap": true, "taper": true, "easing": "linear"}}}}, {"type": "default", "freedrawOnly": false, "strokeColor": "#000000", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 0, "roughness": 0, "penOptions": {"highlighter": false, "constantPressure": false, "hasOutline": false, "outlineWidth": 1, "options": {"thinning": 0.6, "smoothing": 0.5, "streamline": 0.5, "easing": "easeOutSine", "start": {"cap": true, "taper": 0, "easing": "linear"}, "end": {"cap": true, "taper": 0, "easing": "linear"}}}}, {"type": "default", "freedrawOnly": false, "strokeColor": "#000000", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 0, "roughness": 0, "penOptions": {"highlighter": false, "constantPressure": false, "hasOutline": false, "outlineWidth": 1, "options": {"thinning": 0.6, "smoothing": 0.5, "streamline": 0.5, "easing": "easeOutSine", "start": {"cap": true, "taper": 0, "easing": "linear"}, "end": {"cap": true, "taper": 0, "easing": "linear"}}}}, {"type": "default", "freedrawOnly": false, "strokeColor": "#000000", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 0, "roughness": 0, "penOptions": {"highlighter": false, "constantPressure": false, "hasOutline": false, "outlineWidth": 1, "options": {"thinning": 0.6, "smoothing": 0.5, "streamline": 0.5, "easing": "easeOutSine", "start": {"cap": true, "taper": 0, "easing": "linear"}, "end": {"cap": true, "taper": 0, "easing": "linear"}}}}], "numberOfCustomPens": 0, "pdfScale": 4, "pdfBorderBox": true, "pdfFrame": false, "pdfGapSize": 20, "pdfGroupPages": false, "pdfLockAfterImport": true, "pdfNumColumns": 1, "pdfNumRows": 1, "pdfDirection": "right", "pdfImportScale": 0.3, "gridSettings": {"DYNAMIC_COLOR": true, "COLOR": "#000000", "OPACITY": 50, "GRID_DIRECTION": {"horizontal": true, "vertical": true}}, "laserSettings": {"DECAY_LENGTH": 50, "DECAY_TIME": 1000, "COLOR": "#ff0000"}, "embeddableMarkdownDefaults": {"useObsidianDefaults": false, "backgroundMatchCanvas": false, "backgroundMatchElement": true, "backgroundColor": "#fff", "backgroundOpacity": 60, "borderMatchElement": true, "borderColor": "#fff", "borderOpacity": 0, "filenameVisible": false}, "markdownNodeOneClickEditing": false, "canvasImmersiveEmbed": true, "startupScriptPath": "", "aiEnabled": true, "openAIAPIToken": "", "openAIDefaultTextModel": "gpt-3.5-turbo-1106", "openAIDefaultVisionModel": "gpt-4o", "openAIDefaultImageGenerationModel": "dall-e-3", "openAIURL": "https://api.openai.com/v1/chat/completions", "openAIImageGenerationURL": "https://api.openai.com/v1/images/generations", "openAIImageEditsURL": "https://api.openai.com/v1/images/edits", "openAIImageVariationURL": "https://api.openai.com/v1/images/variations", "modifierKeyConfig": {"Mac": {"LocalFileDragAction": {"defaultAction": "image-import", "rules": [{"shift": false, "ctrl_cmd": false, "alt_opt": false, "meta_ctrl": false, "result": "image-import"}, {"shift": true, "ctrl_cmd": false, "alt_opt": true, "meta_ctrl": false, "result": "link"}, {"shift": true, "ctrl_cmd": false, "alt_opt": false, "meta_ctrl": false, "result": "image-url"}, {"shift": false, "ctrl_cmd": false, "alt_opt": true, "meta_ctrl": false, "result": "embeddable"}]}, "WebBrowserDragAction": {"defaultAction": "image-url", "rules": [{"shift": false, "ctrl_cmd": false, "alt_opt": false, "meta_ctrl": false, "result": "image-url"}, {"shift": true, "ctrl_cmd": false, "alt_opt": true, "meta_ctrl": false, "result": "link"}, {"shift": false, "ctrl_cmd": false, "alt_opt": true, "meta_ctrl": false, "result": "embeddable"}, {"shift": true, "ctrl_cmd": false, "alt_opt": false, "meta_ctrl": false, "result": "image-import"}]}, "InternalDragAction": {"defaultAction": "link", "rules": [{"shift": false, "ctrl_cmd": false, "alt_opt": false, "meta_ctrl": false, "result": "link"}, {"shift": false, "ctrl_cmd": false, "alt_opt": false, "meta_ctrl": true, "result": "embeddable"}, {"shift": true, "ctrl_cmd": false, "alt_opt": false, "meta_ctrl": false, "result": "image"}, {"shift": true, "ctrl_cmd": false, "alt_opt": false, "meta_ctrl": true, "result": "image-fullsize"}]}, "LinkClickAction": {"defaultAction": "new-tab", "rules": [{"shift": false, "ctrl_cmd": false, "alt_opt": false, "meta_ctrl": false, "result": "active-pane"}, {"shift": false, "ctrl_cmd": true, "alt_opt": false, "meta_ctrl": false, "result": "new-tab"}, {"shift": false, "ctrl_cmd": true, "alt_opt": true, "meta_ctrl": false, "result": "new-pane"}, {"shift": true, "ctrl_cmd": true, "alt_opt": true, "meta_ctrl": false, "result": "popout-window"}, {"shift": false, "ctrl_cmd": true, "alt_opt": false, "meta_ctrl": true, "result": "md-properties"}]}}, "Win": {"LocalFileDragAction": {"defaultAction": "image-import", "rules": [{"shift": false, "ctrl_cmd": false, "alt_opt": false, "meta_ctrl": false, "result": "image-import"}, {"shift": false, "ctrl_cmd": true, "alt_opt": false, "meta_ctrl": false, "result": "link"}, {"shift": true, "ctrl_cmd": false, "alt_opt": false, "meta_ctrl": false, "result": "image-url"}, {"shift": true, "ctrl_cmd": true, "alt_opt": false, "meta_ctrl": false, "result": "embeddable"}]}, "WebBrowserDragAction": {"defaultAction": "image-url", "rules": [{"shift": false, "ctrl_cmd": false, "alt_opt": false, "meta_ctrl": false, "result": "image-url"}, {"shift": false, "ctrl_cmd": true, "alt_opt": false, "meta_ctrl": false, "result": "link"}, {"shift": true, "ctrl_cmd": true, "alt_opt": false, "meta_ctrl": false, "result": "embeddable"}, {"shift": true, "ctrl_cmd": false, "alt_opt": false, "meta_ctrl": false, "result": "image-import"}]}, "InternalDragAction": {"defaultAction": "link", "rules": [{"shift": false, "ctrl_cmd": false, "alt_opt": false, "meta_ctrl": false, "result": "link"}, {"shift": true, "ctrl_cmd": true, "alt_opt": false, "meta_ctrl": false, "result": "embeddable"}, {"shift": true, "ctrl_cmd": false, "alt_opt": false, "meta_ctrl": false, "result": "image"}, {"shift": false, "ctrl_cmd": true, "alt_opt": true, "meta_ctrl": false, "result": "image-fullsize"}]}, "LinkClickAction": {"defaultAction": "new-tab", "rules": [{"shift": false, "ctrl_cmd": false, "alt_opt": false, "meta_ctrl": false, "result": "active-pane"}, {"shift": false, "ctrl_cmd": true, "alt_opt": false, "meta_ctrl": false, "result": "new-tab"}, {"shift": false, "ctrl_cmd": true, "alt_opt": true, "meta_ctrl": false, "result": "new-pane"}, {"shift": true, "ctrl_cmd": true, "alt_opt": true, "meta_ctrl": false, "result": "popout-window"}, {"shift": false, "ctrl_cmd": true, "alt_opt": false, "meta_ctrl": true, "result": "md-properties"}]}}}, "slidingPanesSupport": false, "areaZoomLimit": 1, "longPressDesktop": 500, "longPressMobile": 500, "doubleClickLinkOpenViewMode": true, "isDebugMode": false, "rank": "Bronze", "modifierKeyOverrides": [{"modifiers": ["Mod"], "key": "Enter"}, {"modifiers": ["Mod"], "key": "k"}, {"modifiers": ["Mod"], "key": "G"}], "showSplashscreen": true, "pdfSettings": {"pageSize": "A4", "pageOrientation": "portrait", "fitToPage": 1, "paperColor": "white", "customPaperColor": "#ffffff", "alignment": "center", "margin": "normal"}}